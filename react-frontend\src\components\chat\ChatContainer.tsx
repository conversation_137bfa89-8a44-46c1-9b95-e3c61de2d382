import React, { useEffect, useRef } from 'react';
import { Message, FileAttachment, User } from '@/types';
import ChatMessages from './ChatMessages';
import ChatInput from './ChatInput';
import SuggestionChips from './SuggestionChips';

interface ChatContainerProps {
  messages: Message[];
  isLoading: boolean;
  attachedFiles: FileAttachment[];
  onSendMessage: (message: string, files?: FileAttachment[]) => void;
  onAddFile: (file: FileAttachment) => void;
  onRemoveFile: (fileId: string) => void;
  onOpenVoice: () => void;
  onOpenEscalation: () => void;
  user: User | null;
}

const ChatContainer: React.FC<ChatContainerProps> = ({
  messages,
  isLoading,
  attachedFiles,
  onSendMessage,
  onAddFile,
  onRemoveFile,
  onOpenVoice,
  onOpenEscalation,
  user,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const suggestions = [
    { text: "🗓️ Leave Policy", query: "What is the company's leave policy?" },
    { text: "👥 Referral Program", query: "How does the employee referral program work?" },
    { text: "👔 Dress Code", query: "What is the dress code policy?" },
    { text: "🏠 Work from Home", query: "Tell me about the work from home policy" },
    { text: "💼 Benefits", query: "What are the company benefits?" },
    { text: "📅 Request Time Off", query: "How do I request time off?" },
  ];

  const handleSuggestionClick = (query: string) => {
    onSendMessage(query);
  };

  return (
    <div className="main-content" id="mainContent">
      {/* Chat messages area */}
      <div className="chat-messages-container" id="chatMessagesContainer">
        {messages.length === 0 ? (
          // Welcome message for logged-in users
          <div className="welcome-container" id="welcomeContainer">
            <div className="welcome-message">
              <h2>👋 Welcome to ZiaHR</h2>
              <p>I can help you with questions about company policies, employee guidelines, and HR procedures.</p>
              <div className="suggestion-chips" style={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', flexWrap: 'wrap', gap: '16px' }}>
                {suggestions.slice(0, 4).map((suggestion, index) => (
                  <button
                    key={index}
                    className="suggestion-chip"
                    onClick={() => handleSuggestionClick(suggestion.query)}
                  >
                    {suggestion.text}
                  </button>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <div className="chat-messages" id="chatMessages">
            <ChatMessages
              messages={messages}
              isLoading={isLoading}
            />
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Chat input area */}
      <div className="chat-input-container" id="chatInputContainer">
        <ChatInput
          attachedFiles={attachedFiles}
          onSendMessage={onSendMessage}
          onAddFile={onAddFile}
          onRemoveFile={onRemoveFile}
          onOpenVoice={onOpenVoice}
          onOpenEscalation={onOpenEscalation}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
};

export default ChatContainer;
