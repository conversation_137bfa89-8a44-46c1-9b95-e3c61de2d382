import React, { useState, useRef } from 'react';
import { FileAttachment } from '@/types';

interface ChatInputProps {
  attachedFiles: FileAttachment[];
  onSendMessage: (message: string, files?: FileAttachment[]) => void;
  onAddFile: (file: FileAttachment) => void;
  onRemoveFile: (fileId: string) => void;
  onOpenVoice: () => void;
  onOpenEscalation: () => void;
  isLoading: boolean;
}

const ChatInput: React.FC<ChatInputProps> = ({
  attachedFiles,
  onSendMessage,
  onAddFile,
  onRemoveFile,
  onOpenVoice,
  onOpenEscalation,
  isLoading,
}) => {
  const [inputValue, setInputValue] = useState('');
  const [showSendButton, setShowSendButton] = useState(false);
  const [showEscalationButton, setShowEscalationButton] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setInputValue(value);
    setShowSendButton(value.trim().length > 0 || attachedFiles.length > 0);
    
    // Show escalation button if message contains escalation keywords
    const escalationKeywords = ['escalate', 'hr', 'complaint', 'issue', 'problem', 'help'];
    const hasEscalationKeyword = escalationKeywords.some(keyword => 
      value.toLowerCase().includes(keyword)
    );
    setShowEscalationButton(hasEscalationKeyword);
    
    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if ((inputValue.trim() || attachedFiles.length > 0) && !isLoading) {
      onSendMessage(inputValue.trim(), attachedFiles);
      setInputValue('');
      setShowSendButton(false);
      setShowEscalationButton(false);
      
      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      Array.from(files).forEach((file) => {
        const fileAttachment: FileAttachment = {
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          name: file.name,
          type: file.type,
          size: file.size,
          url: URL.createObjectURL(file),
        };
        onAddFile(fileAttachment);
      });
    }
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const renderAttachedFiles = () => {
    if (attachedFiles.length === 0) return null;

    return (
      <div className="mb-3 flex flex-wrap gap-2">
        {attachedFiles.map((file) => (
          <div
            key={file.id}
            className="flex items-center space-x-2 bg-primary-secondary dark:bg-dark-secondary px-3 py-2 rounded-lg border border-primary-border dark:border-dark-border"
          >
            <i className="fas fa-file text-primary-text-secondary dark:text-dark-text-secondary"></i>
            <span className="text-sm text-primary-text dark:text-dark-text">
              {file.name}
            </span>
            <button
              onClick={() => onRemoveFile(file.id)}
              className="text-primary-text-secondary dark:text-dark-text-secondary hover:text-primary-text dark:hover:text-dark-text"
            >
              <i className="fas fa-times text-xs"></i>
            </button>
          </div>
        ))}
      </div>
    );
  };

  return (
    <form className="chat-input-form" onSubmit={handleSubmit}>
      <div className={`chatgpt-input-wrapper ${inputValue.trim().length > 0 ? 'has-text' : ''}`}>
        {/* Main textarea at top */}
        <div className="chatgpt-input-area">
          <label htmlFor="chatInput" style={{ position: 'absolute', left: '-9999px', top: 'auto', width: '1px', height: '1px', overflow: 'hidden' }}>
            Type your question here
          </label>
          <textarea
            ref={textareaRef}
            id="chatInput"
            placeholder="Ask anything about leaves, benefits, or company policies"
            rows={1}
            aria-label="Type your question here"
            aria-multiline="true"
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            disabled={isLoading}
          />
        </div>

        {/* Bottom tools section with only our 3 functionalities */}
        <div className="chatgpt-bottom-tools">
          <div className="chatgpt-left-tools">
            <button
              type="button"
              className="chatgpt-tool-btn"
              onClick={() => fileInputRef.current?.click()}
              title="Upload Documents"
              disabled={isLoading}
            >
              <i className="fas fa-plus"></i>
            </button>
            <input
              ref={fileInputRef}
              type="file"
              onChange={handleFileUpload}
              accept=".pdf,.docx,.txt,.md"
              multiple
              style={{ display: 'none' }}
            />

            {/* Escalation button */}
            {showEscalationButton && (
              <button
                type="button"
                className="chatgpt-tool-btn"
                onClick={onOpenEscalation}
                title="Escalate to HR"
                disabled={isLoading}
              >
                <i className="fas fa-exclamation-circle" style={{ color: '#dc3545' }}></i>
              </button>
            )}
          </div>

          <div className="chatgpt-right-tools">
            {/* Only voice input */}
            <button
              type="button"
              className="chatgpt-tool-btn"
              onClick={onOpenVoice}
              title="Voice Input"
              disabled={isLoading}
            >
              <i className="fas fa-microphone"></i>
            </button>

            {/* Send button (appears when typing) */}
            {showSendButton && (
              <button
                type="submit"
                className="chatgpt-send-btn"
                title="Send message"
                aria-label="Send message"
                disabled={isLoading}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7 11L12 6L17 11M12 18V7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* File list container (hidden, used for internal tracking) */}
      <div id="fileListContainer" className="file-list-container" style={{ display: 'none' }}></div>
      
      {/* Input attachments container (for showing files in input area) */}
      <div id="inputAttachmentsContainer" className="input-attachments-container" style={{ display: 'none' }}></div>
      
      {/* File display area */}
      <div className="file-display-area">
        {/* Files will be dynamically added here by JavaScript */}
      </div>

      {/* Suggestion buttons for logged-in users only */}
      <div className="input-suggestions logged-in-only" style={{ marginTop: '12px', display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', flexWrap: 'wrap', gap: '12px' }}>
        <button className="suggestion-chip" onClick={() => onSendMessage("What is the company's leave policy?")}>
          🗓️ Leave Policy
        </button>
        <button className="suggestion-chip" onClick={() => onSendMessage("How does the employee referral program work?")}>
          👥 Referral Program
        </button>
        <button className="suggestion-chip" onClick={() => onSendMessage("What is the dress code policy?")}>
          👔 Dress Code
        </button>
        <button className="suggestion-chip" onClick={() => onSendMessage("Tell me about the work from home policy")}>
          🏠 Work from Home
        </button>
      </div>
    </form>
  );
};

export default ChatInput;
