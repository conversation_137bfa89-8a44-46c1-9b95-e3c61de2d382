import React, { useState } from 'react';
import { ChatSession } from '@/types';

interface SidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
  chatSessions: ChatSession[];
  currentSessionId: string | null;
  onNewChat: () => void;
  onLoadSession: (sessionId: string) => void;
  onDeleteSession: (sessionId: string) => void;
  onArchiveSession: (sessionId: string) => void;
  onOpenSearch: () => void;
  onOpenArchivedChats: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  isCollapsed,
  onToggle,
  chatSessions,
  currentSessionId,
  onNewChat,
  onLoadSession,
  onDeleteSession,
  onArchiveSession,
  onOpenSearch,
  onOpenArchivedChats,
}) => {
  const [hoveredSession, setHoveredSession] = useState<string | null>(null);

  const formatDate = (date: string | Date) => {
    const d = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffInHours = (now.getTime() - d.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return 'Today';
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      return d.toLocaleDateString();
    }
  };

  const truncateTitle = (title: string, maxLength: number = 30) => {
    return title.length > maxLength ? title.substring(0, maxLength) + '...' : title;
  };

  return (
    <div className={`sidebar ${isCollapsed ? 'collapsed' : ''}`} id="sidebar">
      {/* Sidebar header with toggle and app icon */}
      <div className="sidebar-header" style={{ gap: '8px', padding: '4px 8px', minHeight: '32px' }}>
        <div className="sidebar-brand-logo" id="appIconBtn" tabIndex={0} title="Home" style={{ margin: '0 4px' }}>
          <img src="/favicon.png" alt="ZiaHR" className="sidebar-app-icon" />
        </div>
        <button id="toggleSidebar" className="sidebar-toggle" onClick={onToggle}>
          <i className="fas fa-bars"></i>
        </button>
      </div>

      {/* Sidebar menu items */}
      <nav className="sidebar-nav">
        <div className="sidebar-menu">
          <div className="sidebar-menu-item" id="sidebarNewChatBtn" onClick={onNewChat}>
            <img src="/new-chat-icon-larger.svg" alt="New Chat" className="sidebar-menu-icon" style={{ width: '20px', height: '20px', marginRight: '8px' }} />
            <span className="sidebar-menu-text">New Chat</span>
          </div>
          <div className="sidebar-menu-item" id="sidebarSearchBtn" onClick={onOpenSearch}>
            <i className="fas fa-search sidebar-menu-icon"></i>
            <span className="sidebar-menu-text">Search chats</span>
          </div>
          <div className="sidebar-menu-item">
            <i className="fas fa-users sidebar-menu-icon"></i>
            <span className="sidebar-menu-text">HR Team</span>
          </div>
        </div>

        {/* Chat history section */}
        <div className="sidebar-conversations">
          <div className="sidebar-section-header">
            <span className="sidebar-section-title">Chats</span>
          </div>
          <div className="chat-history-list" id="chatHistory">
            {chatSessions.map((session) => (
              <div
                key={session.id}
                className={`chat-history-item ${currentSessionId === session.id ? 'active' : ''}`}
                onClick={() => onLoadSession(session.id)}
                onMouseEnter={() => setHoveredSession(session.id)}
                onMouseLeave={() => setHoveredSession(null)}
              >
                <div className="chat-title">{truncateTitle(session.title)}</div>
                <div className="chat-date">{formatDate(session.updatedAt)}</div>
                {hoveredSession === session.id && (
                  <div className="chat-actions">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onArchiveSession(session.id);
                      }}
                      title="Archive"
                    >
                      <i className="fas fa-archive"></i>
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onDeleteSession(session.id);
                      }}
                      title="Delete"
                    >
                      <i className="fas fa-trash"></i>
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Sidebar bottom */}
        <div className="sidebar-bottom">
          <form id="uploadForm" className="upload-form">
            <input type="file" id="fileUpload" accept=".pdf,.docx,.txt,.md" hidden multiple />
          </form>
          <div id="uploadStatus" className="upload-status" style={{ display: 'none' }}></div>
        </div>
      </nav>
    </div>

  );
};

export default Sidebar;
